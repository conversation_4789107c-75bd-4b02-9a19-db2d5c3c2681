import urllib
import execjs
import requests
import json
from loguru import logger
import requests
import json


headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "cache-control": "no-cache",
    "content-type": "application/json;charset=UTF-8",
    "origin": "https://bscm.jinritemai.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create?",
    "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0",
    "x-ecom-platform-source": "uscmSupplyDistritionDpu",
    "x-secsdk-csrf-token": "000100000001e96e83f3beb9ac14ea130bb19691b068ffd87e258cf644f5b3740c5640f83b0b185f7c7e8564db89",
    "x-tt-from-appid": "ffa-goods",
    "x-tt-from-end": "PC",
    "x-tt-from-page": "https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create",
    "x-tt-from-version": "1.0.0.1716"
}
cookies = {
    "gfkadpd": "708,29161",
    "s_v_web_id": "verify_metaizqi_SZrB1eCM_y5h3_4OhC_8iKw_510oe83HrtSX",
    "passport_csrf_token": "5ac434d12722b2ba51f7b12b971c3894",
    "passport_csrf_token_default": "5ac434d12722b2ba51f7b12b971c3894",
    "csrf_session_id": "0854c02860aeed681f25918f7277e229",
    "MONITOR_WEB_ID": "089100e0-1e36-4e98-a9d5-54c291b2a542",
    "ttcid": "a314acab513242bb8dae2076b8bd8cb527",
    "passport_mfa_token": "CjdRvllGWcfRkC4CubEqQT12bAXzH00tX%2FGZgr9XUerzs3%2BfQmLyt23xO7nPu6oHDyOxvq5c5ebPGkoKPAAAAAAAAAAAAABPZ%2BZmD7Kr5lEv80luyo16RYrtBkDcZTicstN5%2FVTAgH0ZHnkqXaU5I1UJ4OYNB5b0FBDrxvoNGPax0WwgAiIBA%2FaeYQI%3D",
    "d_ticket": "d5f4fa1e012c974db9f8322952f657e6ed862",
    "n_mh": "byUl6xpgvVJ9Fs5RTqf6Ds-cOmsYsC88q6zAsDoWlQ0",
    "sso_auth_status": "572912d27e1cb5fe97b620325c0b8234",
    "sso_auth_status_ss": "572912d27e1cb5fe97b620325c0b8234",
    "sso_uid_tt": "d1d1cdbde47ea19838d6f3e4182baf5f",
    "sso_uid_tt_ss": "d1d1cdbde47ea19838d6f3e4182baf5f",
    "toutiao_sso_user": "d07f95178660d854e06468a2d7db6124",
    "toutiao_sso_user_ss": "d07f95178660d854e06468a2d7db6124",
    "sid_ucp_sso_v1": "1.0.0-KDFmYmFkNmNhNTA4NGJmMWRlMzg4MTEzZWYxOWY5YmVkNzAzYTQ3ZjEKHwiftbDmt82QBBDDvbnFBhiwISAMMNCY36UGOAJA8QcaAmhsIiBkMDdmOTUxNzg2NjBkODU0ZTA2NDY4YTJkN2RiNjEyNA",
    "ssid_ucp_sso_v1": "1.0.0-KDFmYmFkNmNhNTA4NGJmMWRlMzg4MTEzZWYxOWY5YmVkNzAzYTQ3ZjEKHwiftbDmt82QBBDDvbnFBhiwISAMMNCY36UGOAJA8QcaAmhsIiBkMDdmOTUxNzg2NjBkODU0ZTA2NDY4YTJkN2RiNjEyNA",
    "is_staff_user": "false",
    "_csrf_img_k": "ecom_bscp_imagex_token",
    "passport_auth_status": "1cd4b62f2c2d43eb5ab122af38f14635%2Cd4eb3e47509d3dc9de3b8ec43cbd4559",
    "passport_auth_status_ss": "1cd4b62f2c2d43eb5ab122af38f14635%2Cd4eb3e47509d3dc9de3b8ec43cbd4559",
    "uid_tt": "a3ef13d89e985bbb51c70b9dc79f6649",
    "uid_tt_ss": "a3ef13d89e985bbb51c70b9dc79f6649",
    "sid_tt": "c56abea86211e979bfb12185cabd0b50",
    "sessionid": "c56abea86211e979bfb12185cabd0b50",
    "sessionid_ss": "c56abea86211e979bfb12185cabd0b50",
    "session_tlb_tag": "sttt%7C3%7CxWq-qGIR6Xm_sSGFyr0LUP________-jQPHCSiumBBkpTcKZtZOl2eSHiHl9h3K_Ukty6FKOP-I%3D",
    "odin_tt": "67c28806a77370c2235ab0a4747cd3a7b7186d4c86b15d052fa847b0d453f2b4445eb5e3a2053fe44b8c62d9fcaf283c82d5fd82cc09524762e0563a5e9adba7",
    "need_choose_shop": "0",
    "ffa_goods_ewid": "undefined",
    "ffa_goods_seraph_did": "undefined",
    "ttwid": "1%7CBKFvrdZ1xeSQO08alLOan2_UedK2Fj4Ivh4c1uMRmd4%7C1756259262%7C62120e6ce70b97a288f81f05be3db506f73b16bb0dd1bcafea79c5a53d87ce97",
    "ucas_c0_bscm": "CkQKBTEuMC4wEJuIgsbC95jXaBjD0BIg6ZzQiavN3wIo-MESMJ-1sOa3zZAEQL_HucUGSL_79ccGUKK8kJaagNataFisARIU9uSsXH3FMM-iTDxH8ax5e0NDT1k",
    "ucas_c0_ss_bscm": "CkQKBTEuMC4wEJuIgsbC95jXaBjD0BIg6ZzQiavN3wIo-MESMJ-1sOa3zZAEQL_HucUGSL_79ccGUKK8kJaagNataFisARIU9uSsXH3FMM-iTDxH8ax5e0NDT1k",
    "sid_guard": "c56abea86211e979bfb12185cabd0b50%7C1756259263%7C5184000%7CSun%2C+26-Oct-2025+01%3A47%3A43+GMT",
    "sid_ucp_v1": "1.0.0-KDQ1NDAxNzI5ZTY2MDc2NzAwMjQ2OWUzMmY3YzQ0MjdkNGM5ZjZhNzEKGgiftbDmt82QBBC_x7nFBhj4wRIgDDgCQPEHGgJsZiIgYzU2YWJlYTg2MjExZTk3OWJmYjEyMTg1Y2FiZDBiNTA",
    "ssid_ucp_v1": "1.0.0-KDQ1NDAxNzI5ZTY2MDc2NzAwMjQ2OWUzMmY3YzQ0MjdkNGM5ZjZhNzEKGgiftbDmt82QBBC_x7nFBhj4wRIgDDgCQPEHGgJsZiIgYzU2YWJlYTg2MjExZTk3OWJmYjEyMTg1Y2FiZDBiNTA",
    "tt_scid": "0zlBj96iBZxjZJB.kXRILjV59O6t.gv5bLmEYg1Fi1JEVcD83m1.6G4lwcdUrNJG08ae",
    "gd_random": "eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC4wNTM4NDY2OTY5OTQxODY1NH0=.uYWcqhwK7pbfiQg0C4CQac7jp/dRD295OQXQ2raoY/4="
}
url = "https://bscm.jinritemai.com/fxg/product/tproduct/addWithSchema"
params = {
    "check_status": "2",
    "_bid": "ffa_goods",
    "_lid": "598899988582",
    "verifyFp": "verify_metaizqi_SZrB1eCM_y5h3_4OhC_8iKw_510oe83HrtSX",
    "fp": "verify_metaizqi_SZrB1eCM_y5h3_4OhC_8iKw_510oe83HrtSX",
    "msToken": "8qw6kGi2EFAyP_BNUhjLXafMPOeza-OCAIp5GNxbesP838tir_GZjKMoxYsP2M4Reop96dXJya_3iQOR07emD_QH2rFE8p9HiFxke83TkwC5k_yxdieTdg==",
    # "a_bogus": "DfmwQRhfmEVNffYg5I9LfY3qVekzYBw80aXYMD2a7oppJ639HMTv9exYlovvpU8jis/mIeujy4hbYN9krQA9MZwfHuix/2xDmESkKl5Q5xSSs1XJtyUgnzUimktUCec2-i-lrOXMoh1HFbjm09oHmhK4bIOwu3GMiD=="
}
data = {
    "schema": {
        "model": {
            "sku_detail": {
                "value": [
                    {
                        "id": "771d6b7dcd3c-a0ae5e-fef11048f67c",
                        "stock_info": {
                            "stock_num": 0
                        },
                        "confirm_no_barcode": False,
                        "spec_detail_ids": [
                            "993119712366547767"
                        ],
                        "supply_price": "22",
                        "price": "33"
                    }
                ]
            },
            "spec_detail": {
                "value": [
                    {
                        "id": "991332463400875981",
                        "is_default": True,
                        "name": "默认",
                        "spec_values": [
                            {
                                "id": "993119712366547767",
                                "name": "默认"
                            }
                        ]
                    }
                ]
            },
            "white_background_pic": {
                "value": None
            },
            "title": {
                "value": "国风凤凰草本纹身贴持久防水不反光高级逼真适合男女"
            },
            "pic": {
                "value": [
                    {
                        "url": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/jpeg_m_33d01ac4b002461f308bff49de6a3ece_sx_554340_www2250-2250"
                    }
                ]
            },
            "after_sale": {
                "value": {
                    "quality_problem_return": {
                        "option_id": None,
                        "selected": True
                    },
                    "supply_day_return_selector": {
                        "option_id": "7-1",
                        "selected": True
                    }
                }
            },
            "category_properties": {
                "value": {
                    "1687": [
                        {
                            "value_id": "596120136",
                            "value_name": "无品牌",
                            "tags": {
                                "brand_cn_name": ""
                            }
                        }
                    ]
                }
            },
            "delivery_delay_day": {
                "value": "1"
            },
            "description": {
                "value": "<p><img src=\"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/jpeg_m_33d01ac4b002461f308bff49de6a3ece_sx_554340_www2250-2250\" style=\"max-width:100%;\"/></p>"
            },
            "freight_id": {
                "value": "1246374210"
            },
            "goods_category": {
                "value": {
                    "category_leaf_id": 22698,
                    "first_cid": 20013,
                    "first_cname": "五金/工具",
                    "fourth_cid": 22698,
                    "fourth_cname": "其他套筒及配件",
                    "second_cid": 20284,
                    "second_cname": "手动工具",
                    "third_cid": 38242,
                    "third_cname": "套筒及配件"
                }
            },
            "pickup_method": {
                "value": "0"
            },
            "presell_type": {
                "value": "0"
            },
            "product_type": {
                "value": "0"
            },
            "qualification": {
                "value": {}
            },
            "reduce_type": {
                "value": "1"
            },
            "return_address": {
                "value": "62214"
            },
            "short_product_name": {
                "value": ""
            },
            "title_prefix": {
                "value": ""
            },
            "title_suffix": {
                "value": ""
            },
            "title_use_brand_name": {
                "value": False
            },
            "restricted_purchasing_plan": {
                "value": "blank"
            },
            "detail_prettify_uri": {
                "value": "detail_prettify_4e0bb9fff5f6c305ca7f94b82a0d2da7_1ur5QO"
            }
        },
        "context": {
            "ability": [],
            "biz_identity": "xiaodian",
            "category_id": "22698",
            "fast_publish_type": "",
            "feature": {
                "not_first_render": "1",
                "session_data": "{\"stock_incr_mode\":false,\"only_update_stock\":null}"
            },
            "gray_components": [
                "alli_promotion_plan_switch",
                "deposit_is_select",
                "deposit_price",
                "deposit_find_time",
                "common_reject",
                "white_background_pic",
                "product_type",
                "auction_type",
                "start_sale_type",
                "enable_all_channel_product_online",
                "is_evaluate_opened",
                "after_sale",
                "supply_day_return_selector",
                "damaged_order_return",
                "support_authentic_guaranteeV2",
                "support_allergy_returnV2",
                "supply_allergy_return",
                "quality_problem_return",
                "supply_red_ass_return",
                "worry_free_settlement",
                "is_large_product",
                "three_guarantees",
                "fix_duration",
                "extended_duration",
                "dcar_coupon_rights",
                "use_gold_price",
                "gold_price_type",
                "poi_code_type",
                "poi_coupon_return_methods",
                "poi_total_can_use_count",
                "poi_condition",
                "poi_link",
                "poi_valid_range",
                "poi_service_num",
                "poi_notification",
                "poi_lib_id",
                "poi_financial_settlement_rate",
                "poi_ids",
                "poi_valid_type",
                "poi_valid_days",
                "cp_contract_info",
                "contract_interest_subsidy_switch",
                "spec_detail",
                "use_old_spec",
                "outer_product_id",
                "#notification",
                "pic",
                "main_image_three_to_four",
                "sale_channel_type",
                "account_template_id",
                "qualification",
                "is_c2b_switch_on",
                "micro_app_id",
                "shop_category",
                "weight_unit",
                "weight_value",
                "total_buy_num",
                "max_buy_num",
                "min_buy_num",
                "interest_free_open",
                "interest_free_activity_id",
                "interest_free_activity",
                "is_hainan_post",
                "is_hainan_pick",
                "category_properties",
                "category_property_prefill",
                "category_property_prefill_spu",
                "category_property_prefill_barcode",
                "quality_inspection_info",
                "nutritional_information",
                "reduce_type",
                "category_property_pic",
                "customs_clear_type",
                "cdf_category",
                "cross_warehouse_id",
                "origin_country_id",
                "source_country_id",
                "brand_country_id",
                "tax_payer",
                "net_weight_qty",
                "return_address",
                "custom_property",
                "refund_tips",
                "title",
                "title_prefix",
                "title_suffix",
                "title_use_brand_name",
                "title_struct",
                "title_switcher",
                "freight_id",
                "product_desc_text",
                "edu_discount",
                "main_pic_video",
                "goods_category",
                "restricted_purchasing_plan",
                "description",
                "detail_prettify_uri",
                "detail_prettify_info",
                "pickup_method",
                "presell_type",
                "delivery_delay_day",
                "appoint_delivery_switch",
                "appoint_delivery_day",
                "delay_rule_switch",
                "delay_rule_order_time",
                "delay_rule_delivery_day",
                "delay_rule_delivery_date",
                "presell_end_time_switch",
                "presell_end_time",
                "presell_delivery_type",
                "presell_delay",
                "presell_time",
                "quality_control",
                "gx_freight_id",
                "privilege_service",
                "long_pic",
                "is_auto_charge",
                "dcar_coupon_type",
                "reference_price",
                "reference_price_certificate_type",
                "reference_price_certificate_urls",
                "promotion_goods_coupon_comp",
                "mass_auction_rules",
                "inner_shop_category",
                "sku_detail",
                "area_stock_switcher",
                "default_process_time",
                "product_instant_discount_coupon",
                "product_promotion",
                "car_vin_code",
                "first_charge_verification",
                "item_max_per_order",
                "size_info_template_id",
                "search_strategy_2c",
                "short_product_name",
                "market_price",
                "product_ingredients"
            ],
            "model_type": "dpu",
            "n_token": "20250827095555C047C8FDA2FE3494571E",
            "operation_type": "normal",
            "product_id": "0",
            "shop_id": "208429226",
            "token": "20250827095554C047C8FDA2FE3494571E",
            "version": "v1_v8_v9"
        }
    },
    "category_id": "22698",
    "pass_through_extra": {},
    "request_extra": {
        "_aToken": "VkZaU41VtUXdOVlZSV0dSUFpXMWtNMVJyVWtOU2F6RlZXWHBHVDFKSGMzZFVWRUpYVWpBeGNsSnJWbE5oYldoRlZHNXdVbVF4UmpOUVZEQTk="
    },
    "check_status": 2,
    "session": {
        "title_recommend_info": {
            "title_name": "国风凤凰草本纹身贴持久防水不反光高级逼真适合男女",
            "title_recruit_info": {
                "title_follow_id": 0,
                "recruit_recommend_text": "根据同款商品潜力热点词生成，预估使用后更易获得流量",
                "short_info_text": "更易获得流量"
            },
            "include_high_words": None,
            "traction_tips": None
        }
    },
    "_bid": "ffa_goods",
    "_lid": "598899983117"
}
data = json.dumps(data, separators=(',', ':'))
params_str = urllib.parse.urlencode(params)
url1 = url + '?' + params_str
ab = execjs.compile(open('ab_192.js', 'r', encoding='utf-8').read()).call('getABogus', url1, data,headers['user-agent'])
logger.info(str(len(ab)) + "   " + ab)
params['a_bogus'] = ab
response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data)

print(response.text)
print(response)